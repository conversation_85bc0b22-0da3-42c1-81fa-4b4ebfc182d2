import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON>ert,
	<PERSON>nack<PERSON>,
	CircularProgress,
	Backdrop,
} from '@mui/material'
import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { useAppStore } from '../core/store'
import { updateLocale } from '../core/i18n'
import { resetSettings } from '../core/utils/settings'
import { ImportPlannedWork } from '../app/settings/components/ImportPlannedWork'
import { DEFAULT_TASK_STATE_COLORS } from '../common/utils/taskStateColors'
import LoginModal from '../common/components/LoginModal'
import SignupModal from '../common/components/SignupModal'
import PasswordResetModal from '../common/components/PasswordResetModal'
import { useAuth } from '../common/hooks/useAuth'
import { AccountManagement } from '../app/settings/components/AccountManagement'
import { AccessibilitySettings } from '../app/settings/components/AccessibilitySettings'

export default function SettingsPage() {
	const navigate = useNavigate()
	const { t } = useTranslation()
	const [notification, setNotification] = useState<{
		message: string
		severity: 'success' | 'error' | 'info'
	} | null>(null)

	// Authentication state using custom hook
	const { isAuthenticated, isLoading, user, logout, handleLoginSuccess } = useAuth()
	const [showLoginModal, setShowLoginModal] = useState(false)
	const [showSignupModal, setShowSignupModal] = useState(false)
	const [showPasswordResetModal, setShowPasswordResetModal] = useState(false)

	const locale = useAppStore((s) => s.locale)
	const setLocale = useAppStore((s) => s.setLocale)
	const fontScale = useAppStore((s) => s.fontScale)
	const setFontScale = useAppStore((s) => s.setFontScale)
	const setThemeMode = useAppStore((s) => s.setThemeMode)
	const taskStateColors = useAppStore((s) => s.taskStateColors)
	const setTaskStateColors = useAppStore((s) => s.setTaskStateColors)

	const showNotification = (
		message: string,
		severity: 'success' | 'error' | 'info' = 'success',
	) => {
		setNotification({ message, severity })
	}

	const closeNotification = () => {
		setNotification(null)
	}

	// Handle successful login
	const handleLogin = (username: string, fullName?: string) => {
		handleLoginSuccess(username, fullName)
		setShowLoginModal(false)
		showNotification(t('auth.loginSuccess', { defaultValue: 'Login successful' }), 'success')
	}

	// Handle logout
	const handleLogout = async () => {
		try {
			await logout()
			// setShowLoginModal(true)
			navigate('/')
			// showNotification(
			// 	t('auth.logoutSuccess', { defaultValue: 'Logged out successfully' }),
			// 	'info',
			// )
		} catch (error) {
			console.error('Logout failed:', error)
			showNotification(t('auth.logoutError', { defaultValue: 'Logout failed' }), 'error')
		}
	}

	// Show login modal when not authenticated
	useEffect(() => {
		if (!isAuthenticated && !isLoading && !showLoginModal) {
			setShowLoginModal(true)
		}
	}, [isAuthenticated, isLoading, showLoginModal])

	async function handleLocaleChange(value: 'en' | 'ja') {
		setLocale(value)
		try {
			await updateLocale(value)
			showNotification(
				t('settings.languageChanged', { defaultValue: 'Language changed successfully' }),
			)
		} catch (error) {
			console.error('Failed to update locale:', error)
			showNotification(
				t('settings.languageError', { defaultValue: 'Failed to change language' }),
				'error',
			)
		}
	}

	const handleFontScaleChange = (value: number) => {
		setFontScale(value)
		showNotification(
			t('settings.fontScaleChanged', { defaultValue: 'Font scale updated' }),
			'info',
		)
	}

	const handleTaskStateColorsChange = (colors: typeof taskStateColors) => {
		setTaskStateColors(colors)
		showNotification(
			t('settings.colorsChanged', { defaultValue: 'Table colors updated' }),
			'info',
		)
	}

	const handleResetSettings = async () => {
		try {
			const defaultSettings = resetSettings()
			setLocale(defaultSettings.locale)
			setThemeMode(defaultSettings.themeMode)
			setFontScale(defaultSettings.fontScale)
			setTaskStateColors(defaultSettings.taskStateColors)
			setTaskStateColors(DEFAULT_TASK_STATE_COLORS)

			// Update i18n if locale changed
			if (defaultSettings.locale !== locale) {
				await updateLocale(defaultSettings.locale)
			}

			showNotification(
				t('settings.resetSuccess', { defaultValue: 'Settings reset to defaults' }),
			)
		} catch (error) {
			console.error('Reset failed:', error)
			showNotification(
				t('settings.resetError', { defaultValue: 'Failed to reset settings' }),
				'error',
			)
		}
	}

	// Show loading backdrop while checking authentication
	if (isLoading) {
		return (
			<Backdrop
				open={true}
				sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
			>
				<CircularProgress color="inherit" />
			</Backdrop>
		)
	}

	return (
		<>
			{/* Login Modal */}
			<LoginModal
				open={showLoginModal}
				onClose={() => setShowLoginModal(false)}
				onSuccess={handleLogin}
			/>

			{/* Signup Modal */}
			<SignupModal
				open={showSignupModal}
				onClose={() => setShowSignupModal(false)}
				onSuccess={(message) => {
					setShowSignupModal(false)
					showNotification(message, 'success')
				}}
			/>

			{/* Password Reset Modal */}
			<PasswordResetModal
				open={showPasswordResetModal}
				onClose={() => setShowPasswordResetModal(false)}
				onSuccess={(message) => {
					setShowPasswordResetModal(false)
					showNotification(message, 'success')
				}}
			/>

			{/* Settings Content - Only show when authenticated */}
			{isAuthenticated && (
				<Box className="space-y-4" sx={{ height: 'calc(100dvh - 100px)', width: '100%', display: 'flex', flexDirection: 'column' }}>
					<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
						<Typography variant="h4" fontWeight={600}>
							{t('settings.title', { defaultValue: 'Settings' })}
						</Typography>
						<Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 2, mr: 8 }}>
							{user && (
								<Typography variant="body2" color="text.secondary">
									{t('auth.welcomeUser', {
										defaultValue: 'User: {{name}}',
										name: user.fullName || user.username,
									})}
								</Typography>
							)}
							<Button variant="outlined" size="small" onClick={handleLogout}>
								<Typography>
									{t('auth.logout', { defaultValue: 'Logout' })}
								</Typography>
							</Button>
						</Box>
					</Box>
					<Box sx={{ height: 'calc(100dvh - 100px)', display: 'flex', flexDirection: 'column', overflow: 'auto' }}>
						<ImportPlannedWork
							onImportSuccess={(message) => showNotification(message, 'success')}
							onImportError={(error) => showNotification(error, 'error')}
						/>
						<AccountManagement
							showSignupModal={() => setShowSignupModal(true)}
							showPasswordResetModal={() => setShowPasswordResetModal(true)}
						/>
						<AccessibilitySettings
							fontScale={fontScale}
							onFontScaleChange={handleFontScaleChange}
							taskStateColors={taskStateColors}
							handleTaskStateColorsChange={handleTaskStateColorsChange}
							handleResetSettings={handleResetSettings}
							locale={locale}
							handleLocaleChange={handleLocaleChange}
						/>
					</Box>
				</Box>
			)}

			{/* Notification Snackbar */}
			{notification && <Snackbar
				open={!!notification}
				autoHideDuration={4000}
				onClose={closeNotification}
				anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
			>
				<Alert
					onClose={closeNotification}
					severity={notification?.severity || 'success'}
					sx={{ width: '100%' }}
				>
					{notification?.message}
				</Alert>
			</Snackbar>}
		</>
	)
}